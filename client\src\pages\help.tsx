import { useTranslation } from 'react-i18next';
import { PageLayout } from '@/components/page-layout';
import { Search, HelpCircle, Book, MessageCircle, Video, FileText } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

export default function Help() {
  const { t } = useTranslation();

  const categories = [
    {
      key: 'gettingStarted',
      icon: Book,
      color: 'bg-blue-500'
    },
    {
      key: 'troubleshooting',
      icon: HelpCircle,
      color: 'bg-red-500'
    },
    {
      key: 'tutorials',
      icon: Video,
      color: 'bg-green-500'
    },
    {
      key: 'api',
      icon: FileText,
      color: 'bg-purple-500'
    }
  ];

  const faqs = [
    'howToRemoveWatermark',
    'supportedFormats',
    'processingTime',
    'qualityLoss',
    'batchProcessing',
    'mobileApp'
  ];

  return (
    <PageLayout 
      title={t('pages.help.title')} 
      subtitle={t('pages.help.subtitle')}
    >
      {/* Search */}
      <div className="max-w-2xl mx-auto mb-12">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <Input 
            placeholder={t('pages.help.search.placeholder')}
            className="pl-10 py-3 text-lg"
          />
        </div>
      </div>

      {/* Categories */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
        {categories.map((category) => {
          const Icon = category.icon;
          return (
            <div key={category.key} className="text-center p-6 border border-gray-200 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
              <div className={`w-16 h-16 mx-auto mb-4 ${category.color} rounded-full flex items-center justify-center`}>
                <Icon className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {t(`pages.help.categories.${category.key}.title`)}
              </h3>
              <p className="text-gray-600 text-sm">
                {t(`pages.help.categories.${category.key}.description`)}
              </p>
            </div>
          );
        })}
      </div>

      {/* Popular Articles */}
      <div className="mb-16">
        <h2 className="text-2xl font-bold text-gray-900 mb-8">
          {t('pages.help.popular.title')}
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {['article1', 'article2', 'article3', 'article4'].map((article) => (
            <div key={article} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow cursor-pointer">
              <h3 className="font-semibold text-gray-900 mb-2">
                {t(`pages.help.popular.${article}.title`)}
              </h3>
              <p className="text-gray-600 text-sm mb-3">
                {t(`pages.help.popular.${article}.description`)}
              </p>
              <div className="text-primary text-sm font-medium">
                {t('pages.help.readMore')} →
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* FAQ */}
      <div className="mb-16">
        <h2 className="text-2xl font-bold text-gray-900 mb-8">
          {t('pages.help.faq.title')}
        </h2>
        <div className="space-y-4">
          {faqs.map((faq) => (
            <div key={faq} className="border border-gray-200 rounded-lg p-6">
              <h3 className="font-semibold text-gray-900 mb-2">
                {t(`pages.help.faq.${faq}.question`)}
              </h3>
              <p className="text-gray-600">
                {t(`pages.help.faq.${faq}.answer`)}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Contact Support */}
      <div className="bg-gray-50 rounded-lg p-8 text-center">
        <MessageCircle className="w-12 h-12 text-primary mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          {t('pages.help.contact.title')}
        </h2>
        <p className="text-gray-600 mb-6">
          {t('pages.help.contact.description')}
        </p>
        <Button className="bg-primary hover:bg-primary/90">
          {t('pages.help.contact.button')}
        </Button>
      </div>
    </PageLayout>
  );
}

import { useTranslation } from 'react-i18next';
import { PageLayout } from '@/components/page-layout';

export default function Cookies() {
  const { t } = useTranslation();

  const sections = [
    'introduction',
    'whatAreCookies',
    'typesOfCookies',
    'howWeUse',
    'thirdParty',
    'manageCookies',
    'updates',
    'contact'
  ];

  return (
    <PageLayout 
      title={t('pages.cookies.title')} 
      subtitle={t('pages.cookies.subtitle')}
    >
      <div className="prose max-w-none">
        <div className="mb-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-blue-800 text-sm mb-0">
            <strong>{t('pages.cookies.lastUpdated')}:</strong> {t('pages.cookies.updateDate')}
          </p>
        </div>

        {sections.map((section) => (
          <section key={section} className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              {t(`pages.cookies.sections.${section}.title`)}
            </h2>
            <div className="text-gray-700 space-y-4">
              <p>{t(`pages.cookies.sections.${section}.content`)}</p>
            </div>
          </section>
        ))}
      </div>
    </PageLayout>
  );
}

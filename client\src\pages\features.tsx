import { useTranslation } from 'react-i18next';
import { PageLayout } from '@/components/page-layout';
import { Zap, Shield, Download, Smartphone, Globe, Cpu } from 'lucide-react';

export default function Features() {
  const { t } = useTranslation();

  const features = [
    {
      icon: Zap,
      key: 'aiPowered',
      gradient: 'from-blue-500 to-purple-600'
    },
    {
      icon: Shield,
      key: 'privacy',
      gradient: 'from-green-500 to-teal-600'
    },
    {
      icon: Download,
      key: 'highQuality',
      gradient: 'from-orange-500 to-red-600'
    },
    {
      icon: Smartphone,
      key: 'mobileFriendly',
      gradient: 'from-pink-500 to-rose-600'
    },
    {
      icon: Globe,
      key: 'multiLanguage',
      gradient: 'from-indigo-500 to-blue-600'
    },
    {
      icon: Cpu,
      key: 'fastProcessing',
      gradient: 'from-purple-500 to-indigo-600'
    }
  ];

  return (
    <PageLayout 
      title={t('pages.features.title')} 
      subtitle={t('pages.features.subtitle')}
    >
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {features.map((feature, index) => {
          const Icon = feature.icon;
          return (
            <div key={feature.key} className="text-center p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
              <div className={`w-16 h-16 mx-auto mb-4 bg-gradient-to-r ${feature.gradient} rounded-full flex items-center justify-center`}>
                <Icon className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                {t(`pages.features.items.${feature.key}.title`)}
              </h3>
              <p className="text-gray-600">
                {t(`pages.features.items.${feature.key}.description`)}
              </p>
            </div>
          );
        })}
      </div>

      <div className="mt-16 text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-8">
          {t('pages.features.whyChoose.title')}
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-3">
              {t('pages.features.whyChoose.advanced.title')}
            </h3>
            <p className="text-gray-600">
              {t('pages.features.whyChoose.advanced.description')}
            </p>
          </div>
          <div className="p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-3">
              {t('pages.features.whyChoose.easy.title')}
            </h3>
            <p className="text-gray-600">
              {t('pages.features.whyChoose.easy.description')}
            </p>
          </div>
          <div className="p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-3">
              {t('pages.features.whyChoose.free.title')}
            </h3>
            <p className="text-gray-600">
              {t('pages.features.whyChoose.free.description')}
            </p>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}

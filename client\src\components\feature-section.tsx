import { useTranslation } from 'react-i18next';
import { Zap, Clock, Shield, FileImage, Smartphone, Download } from 'lucide-react';

const features = [
  {
    icon: Zap,
    key: 'aiPowered',
    gradient: 'from-primary to-primary/80',
  },
  {
    icon: Clock,
    key: 'instant',
    gradient: 'from-secondary to-secondary/80',
  },
  {
    icon: Shield,
    key: 'secure',
    gradient: 'from-accent to-accent/80',
  },
  {
    icon: FileImage,
    key: 'formats',
    gradient: 'from-purple-500 to-purple-600',
  },
  {
    icon: Smartphone,
    key: 'mobile',
    gradient: 'from-green-500 to-green-600',
  },
  {
    icon: Download,
    key: 'noWatermark',
    gradient: 'from-pink-500 to-pink-600',
  },
];

export const FeatureSection = () => {
  const { t } = useTranslation();

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-6">
            {t('features.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('features.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <div 
                key={feature.key}
                className={`text-center p-8 rounded-2xl glass-card interactive-hover feature-card animate-slide-up animate-delay-${(index % 3 + 1) * 100} opacity-0`}
              >
                <div className={`w-16 h-16 mx-auto mb-6 bg-gradient-to-r ${feature.gradient} rounded-full flex items-center justify-center animate-scale-in`}>
                  <Icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  {t(`features.${feature.key}.title`)}
                </h3>
                <p className="text-gray-600">
                  {t(`features.${feature.key}.description`)}
                </p>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

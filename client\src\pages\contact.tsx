import { useTranslation } from 'react-i18next';
import { PageLayout } from '@/components/page-layout';
import { Mail, Phone, MapPin, Clock, Send } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

export default function Contact() {
  const { t } = useTranslation();

  const contactInfo = [
    {
      key: 'email',
      icon: Mail,
      value: '<EMAIL>',
      color: 'bg-blue-500'
    },
    {
      key: 'phone',
      icon: Phone,
      value: '+****************',
      color: 'bg-green-500'
    },
    {
      key: 'address',
      icon: MapPin,
      value: '123 Tech Street, San Francisco, CA 94105',
      color: 'bg-red-500'
    },
    {
      key: 'hours',
      icon: Clock,
      value: 'Mon-Fri: 9AM-6PM PST',
      color: 'bg-purple-500'
    }
  ];

  return (
    <PageLayout 
      title={t('pages.contact.title')} 
      subtitle={t('pages.contact.subtitle')}
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {/* Contact Form */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            {t('pages.contact.form.title')}
          </h2>
          <form className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="firstName">{t('pages.contact.form.firstName')}</Label>
                <Input id="firstName" placeholder={t('pages.contact.form.firstNamePlaceholder')} />
              </div>
              <div>
                <Label htmlFor="lastName">{t('pages.contact.form.lastName')}</Label>
                <Input id="lastName" placeholder={t('pages.contact.form.lastNamePlaceholder')} />
              </div>
            </div>
            
            <div>
              <Label htmlFor="email">{t('pages.contact.form.email')}</Label>
              <Input id="email" type="email" placeholder={t('pages.contact.form.emailPlaceholder')} />
            </div>
            
            <div>
              <Label htmlFor="subject">{t('pages.contact.form.subject')}</Label>
              <Input id="subject" placeholder={t('pages.contact.form.subjectPlaceholder')} />
            </div>
            
            <div>
              <Label htmlFor="message">{t('pages.contact.form.message')}</Label>
              <Textarea 
                id="message" 
                rows={6}
                placeholder={t('pages.contact.form.messagePlaceholder')} 
              />
            </div>
            
            <Button className="w-full bg-primary hover:bg-primary/90">
              <Send className="w-4 h-4 mr-2" />
              {t('pages.contact.form.submit')}
            </Button>
          </form>
        </div>

        {/* Contact Information */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            {t('pages.contact.info.title')}
          </h2>
          <div className="space-y-6 mb-8">
            {contactInfo.map((info) => {
              const Icon = info.icon;
              return (
                <div key={info.key} className="flex items-center space-x-4">
                  <div className={`w-12 h-12 ${info.color} rounded-lg flex items-center justify-center`}>
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900">
                      {t(`pages.contact.info.${info.key}.label`)}
                    </div>
                    <div className="text-gray-600">
                      {info.value}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Response Time */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 className="font-semibold text-blue-900 mb-2">
              {t('pages.contact.response.title')}
            </h3>
            <p className="text-blue-700 text-sm">
              {t('pages.contact.response.description')}
            </p>
          </div>
        </div>
      </div>

      {/* FAQ Section */}
      <div className="mt-16 pt-16 border-t border-gray-200">
        <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
          {t('pages.contact.faq.title')}
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {['question1', 'question2', 'question3', 'question4'].map((q) => (
            <div key={q}>
              <h3 className="font-semibold text-gray-900 mb-2">
                {t(`pages.contact.faq.${q}.question`)}
              </h3>
              <p className="text-gray-600 text-sm">
                {t(`pages.contact.faq.${q}.answer`)}
              </p>
            </div>
          ))}
        </div>
      </div>
    </PageLayout>
  );
}

import { useTranslation } from 'react-i18next';
import { PageLayout } from '@/components/page-layout';
import { Upload, Wand2, Download, CheckCircle } from 'lucide-react';

export default function HowItWorks() {
  const { t } = useTranslation();

  const steps = [
    {
      number: 1,
      key: 'upload',
      icon: Upload,
      gradient: 'from-blue-500 to-cyan-500'
    },
    {
      number: 2,
      key: 'process',
      icon: Wand2,
      gradient: 'from-purple-500 to-pink-500'
    },
    {
      number: 3,
      key: 'download',
      icon: Download,
      gradient: 'from-green-500 to-emerald-500'
    }
  ];

  const tips = [
    'highResolution',
    'clearWatermark',
    'supportedFormats',
    'processingTime'
  ];

  return (
    <PageLayout 
      title={t('pages.howItWorks.title')} 
      subtitle={t('pages.howItWorks.subtitle')}
    >
      {/* Steps */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
        {steps.map((step, index) => {
          const Icon = step.icon;
          return (
            <div key={step.key} className="text-center">
              <div className={`w-20 h-20 mx-auto mb-6 bg-gradient-to-r ${step.gradient} rounded-full flex items-center justify-center text-white text-2xl font-bold relative`}>
                <Icon className="w-10 h-10" />
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-white text-gray-900 rounded-full flex items-center justify-center text-sm font-bold shadow-lg">
                  {step.number}
                </div>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                {t(`pages.howItWorks.steps.${step.key}.title`)}
              </h3>
              <p className="text-gray-600">
                {t(`pages.howItWorks.steps.${step.key}.description`)}
              </p>
            </div>
          );
        })}
      </div>

      {/* Tips Section */}
      <div className="bg-gray-50 rounded-lg p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
          {t('pages.howItWorks.tips.title')}
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {tips.map((tip) => (
            <div key={tip} className="flex items-start space-x-3">
              <CheckCircle className="w-6 h-6 text-green-500 flex-shrink-0 mt-0.5" />
              <div>
                <h3 className="font-semibold text-gray-900 mb-1">
                  {t(`pages.howItWorks.tips.${tip}.title`)}
                </h3>
                <p className="text-gray-600 text-sm">
                  {t(`pages.howItWorks.tips.${tip}.description`)}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* FAQ Section */}
      <div className="mt-16">
        <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
          {t('pages.howItWorks.faq.title')}
        </h2>
        <div className="space-y-6">
          {['question1', 'question2', 'question3'].map((q) => (
            <div key={q} className="border border-gray-200 rounded-lg p-6">
              <h3 className="font-semibold text-gray-900 mb-2">
                {t(`pages.howItWorks.faq.${q}.question`)}
              </h3>
              <p className="text-gray-600">
                {t(`pages.howItWorks.faq.${q}.answer`)}
              </p>
            </div>
          ))}
        </div>
      </div>
    </PageLayout>
  );
}

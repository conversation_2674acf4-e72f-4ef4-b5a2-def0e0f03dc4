@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(20, 14.3%, 4.1%);
  --muted: hsl(60, 4.8%, 95.9%);
  --muted-foreground: hsl(25, 5.3%, 44.7%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(20, 14.3%, 4.1%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(20, 14.3%, 4.1%);
  --border: hsl(20, 5.9%, 90%);
  --input: hsl(20, 5.9%, 90%);
  --primary: hsl(224, 71.4%, 52.2%);
  --primary-foreground: hsl(210, 40%, 98%);
  --secondary: hsl(162, 66.7%, 47.8%);
  --secondary-foreground: hsl(24, 9.8%, 10%);
  --accent: hsl(37, 94.1%, 52.2%);
  --accent-foreground: hsl(24, 9.8%, 10%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(60, 9.1%, 97.8%);
  --ring: hsl(20, 14.3%, 4.1%);
  --radius: 0.5rem;
}

.dark {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(224, 71.4%, 52.2%);
  --primary-foreground: hsl(210, 40%, 98%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  --radius: 0.5rem;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

@layer utilities {
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
  
  .animate-pulse-slow {
    animation: pulse 3s ease-in-out infinite;
  }
  
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out forwards;
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes fadeIn {
  0% { opacity: 0; transform: translateY(20px); }
  100% { opacity: 1; transform: translateY(0px); }
}

/* Custom gradient backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, hsl(224, 71.4%, 52.2%), hsl(224, 71.4%, 42.2%));
}

.gradient-secondary {
  background: linear-gradient(135deg, hsl(162, 66.7%, 47.8%), hsl(162, 66.7%, 37.8%));
}

.gradient-accent {
  background: linear-gradient(135deg, hsl(37, 94.1%, 52.2%), hsl(37, 94.1%, 42.2%));
}

.gradient-hero {
  background: linear-gradient(135deg, hsl(224, 71.4%, 97%), hsl(162, 66.7%, 97%), hsl(37, 94.1%, 97%));
}

.gradient-testimonials {
  background: linear-gradient(135deg, hsl(224, 71.4%, 98%), hsl(162, 66.7%, 98%));
}

/* Upload area hover effects */
.upload-area-hover {
  transition: all 0.3s ease;
}

.upload-area-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Feature cards */
.feature-card {
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Testimonial cards */
.testimonial-card {
  transition: all 0.3s ease;
}

.testimonial-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Button hover effects */
.btn-primary {
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Processing indicators */
.processing-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Language selector */
.language-selector {
  transition: all 0.2s ease;
}

.language-selector:hover {
  background-color: hsl(224, 71.4%, 95%);
}

/* Glassmorphism effects */
.glass-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Enhanced animations */
@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-slide-up {
  animation: slideInUp 0.6s ease-out forwards;
}

.animate-slide-left {
  animation: slideInLeft 0.6s ease-out forwards;
}

.animate-slide-right {
  animation: slideInRight 0.6s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.5s ease-out forwards;
}

/* Staggered animations */
.animate-delay-100 { animation-delay: 0.1s; }
.animate-delay-200 { animation-delay: 0.2s; }
.animate-delay-300 { animation-delay: 0.3s; }
.animate-delay-400 { animation-delay: 0.4s; }
.animate-delay-500 { animation-delay: 0.5s; }

/* Advanced gradients */
.gradient-mesh {
  background: 
    radial-gradient(at 40% 20%, hsla(224, 71%, 60%, 0.7) 0px, transparent 50%),
    radial-gradient(at 80% 0%, hsla(162, 67%, 50%, 0.5) 0px, transparent 50%),
    radial-gradient(at 0% 50%, hsla(37, 94%, 55%, 0.5) 0px, transparent 50%),
    radial-gradient(at 80% 50%, hsla(240, 50%, 80%, 0.3) 0px, transparent 50%),
    radial-gradient(at 0% 100%, hsla(300, 50%, 70%, 0.3) 0px, transparent 50%),
    radial-gradient(at 80% 100%, hsla(180, 50%, 70%, 0.3) 0px, transparent 50%),
    radial-gradient(at 0% 0%, hsla(343, 100%, 76%, 0.3) 0px, transparent 50%);
}

/* Interactive elements */
.interactive-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.interactive-hover:hover {
  transform: translateY(-8px);
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.05);
}

/* Modern card styling */
.modern-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 8px 32px 0 rgba(31, 38, 135, 0.37),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Enhanced mobile responsiveness */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
    line-height: 1.1;
  }
  
  .hero-subtitle {
    font-size: 1.125rem;
  }
  
  .upload-area-hover {
    padding: 2rem 1rem;
  }
  
  .feature-card {
    padding: 1.5rem;
  }
  
  .testimonial-card {
    padding: 1.5rem;
  }
}

@media (max-width: 640px) {
  .hero-title {
    font-size: 2rem;
  }
  
  .feature-card {
    padding: 1rem;
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Image display improvements */
.image-container {
  position: relative;
  overflow: hidden;
}

.image-container img {
  transition: transform 0.3s ease;
}

.image-container:hover img {
  transform: scale(1.02);
}

/* Focus states for accessibility */
button:focus-visible,
a:focus-visible,
select:focus-visible {
  outline: 2px solid hsl(224, 71.4%, 52.2%);
  outline-offset: 2px;
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

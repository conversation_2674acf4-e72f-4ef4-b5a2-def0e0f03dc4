import { useTranslation } from 'react-i18next';
import { PageLayout } from '@/components/page-layout';
import { CheckCircle, AlertCircle, XCircle, Clock } from 'lucide-react';

export default function Status() {
  const { t } = useTranslation();

  const services = [
    {
      key: 'api',
      status: 'operational',
      uptime: '99.9%'
    },
    {
      key: 'processing',
      status: 'operational',
      uptime: '99.8%'
    },
    {
      key: 'storage',
      status: 'operational',
      uptime: '99.9%'
    },
    {
      key: 'cdn',
      status: 'operational',
      uptime: '99.7%'
    }
  ];

  const incidents = [
    {
      key: 'incident1',
      status: 'resolved',
      date: '2024-01-15'
    },
    {
      key: 'incident2',
      status: 'investigating',
      date: '2024-01-10'
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'operational':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'degraded':
        return <AlertCircle className="w-5 h-5 text-yellow-500" />;
      case 'outage':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'investigating':
        return <Clock className="w-5 h-5 text-blue-500" />;
      case 'resolved':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      default:
        return <AlertCircle className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'operational':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'degraded':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'outage':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'investigating':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'resolved':
        return 'text-green-600 bg-green-50 border-green-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <PageLayout 
      title={t('pages.status.title')} 
      subtitle={t('pages.status.subtitle')}
    >
      {/* Overall Status */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
        <div className="flex items-center space-x-3">
          <CheckCircle className="w-8 h-8 text-green-500" />
          <div>
            <h2 className="text-xl font-semibold text-green-900">
              {t('pages.status.overall.title')}
            </h2>
            <p className="text-green-700">
              {t('pages.status.overall.description')}
            </p>
          </div>
        </div>
      </div>

      {/* Services Status */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          {t('pages.status.services.title')}
        </h2>
        <div className="space-y-4">
          {services.map((service) => (
            <div key={service.key} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center space-x-3">
                {getStatusIcon(service.status)}
                <div>
                  <h3 className="font-semibold text-gray-900">
                    {t(`pages.status.services.${service.key}.name`)}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {t(`pages.status.services.${service.key}.description`)}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(service.status)}`}>
                  {t(`pages.status.statuses.${service.status}`)}
                </div>
                <div className="text-gray-600 text-sm mt-1">
                  {t('pages.status.uptime')}: {service.uptime}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Incidents */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          {t('pages.status.incidents.title')}
        </h2>
        <div className="space-y-4">
          {incidents.map((incident) => (
            <div key={incident.key} className="border border-gray-200 rounded-lg p-6">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  {getStatusIcon(incident.status)}
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">
                      {t(`pages.status.incidents.${incident.key}.title`)}
                    </h3>
                    <p className="text-gray-600 text-sm mb-2">
                      {t(`pages.status.incidents.${incident.key}.description`)}
                    </p>
                    <div className="text-gray-500 text-xs">
                      {incident.date}
                    </div>
                  </div>
                </div>
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(incident.status)}`}>
                  {t(`pages.status.statuses.${incident.status}`)}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Subscribe to Updates */}
      <div className="bg-gray-50 rounded-lg p-8 text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          {t('pages.status.subscribe.title')}
        </h2>
        <p className="text-gray-600 mb-6">
          {t('pages.status.subscribe.description')}
        </p>
        <button className="bg-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-primary/90 transition-colors">
          {t('pages.status.subscribe.button')}
        </button>
      </div>
    </PageLayout>
  );
}

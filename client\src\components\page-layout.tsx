import { useTranslation } from 'react-i18next';
import { ArrowLeft, Image } from 'lucide-react';
import { Link } from 'wouter';
import { Button } from '@/components/ui/button';
import { LanguageSelector } from '@/components/language-selector';

interface PageLayoutProps {
  title: string;
  subtitle?: string;
  children: React.ReactNode;
  showBackButton?: boolean;
  backTo?: string;
}

export const PageLayout = ({ 
  title, 
  subtitle, 
  children, 
  showBackButton = true, 
  backTo = "/" 
}: PageLayoutProps) => {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="glass-card shadow-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              {showBackButton && (
                <Link href={backTo}>
                  <Button variant="ghost" size="sm" className="flex items-center space-x-2">
                    <ArrowLeft className="w-4 h-4" />
                    <span>{t('common.back')}</span>
                  </Button>
                </Link>
              )}
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-10 h-10 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center">
                  <Image className="w-6 h-6 text-white" />
                </div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {t('header.title')}
                </h1>
              </Link>
            </div>
            <LanguageSelector />
          </div>
        </div>
      </header>

      {/* Page Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {title}
          </h1>
          {subtitle && (
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {subtitle}
            </p>
          )}
        </div>
        
        <div className="bg-white rounded-lg shadow-sm p-8">
          {children}
        </div>
      </main>
    </div>
  );
};

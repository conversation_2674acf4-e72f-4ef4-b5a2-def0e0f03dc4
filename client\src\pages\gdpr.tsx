import { useTranslation } from 'react-i18next';
import { PageLayout } from '@/components/page-layout';

export default function GDPR() {
  const { t } = useTranslation();

  const sections = [
    'introduction',
    'legalBasis',
    'dataRights',
    'dataProcessing',
    'dataTransfer',
    'dataRetention',
    'security',
    'contact'
  ];

  return (
    <PageLayout 
      title={t('pages.gdpr.title')} 
      subtitle={t('pages.gdpr.subtitle')}
    >
      <div className="prose max-w-none">
        <div className="mb-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-blue-800 text-sm mb-0">
            <strong>{t('pages.gdpr.lastUpdated')}:</strong> {t('pages.gdpr.updateDate')}
          </p>
        </div>

        {sections.map((section) => (
          <section key={section} className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              {t(`pages.gdpr.sections.${section}.title`)}
            </h2>
            <div className="text-gray-700 space-y-4">
              <p>{t(`pages.gdpr.sections.${section}.content`)}</p>
            </div>
          </section>
        ))}

        <div className="mt-12 p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
          <h3 className="text-lg font-semibold text-yellow-900 mb-3">
            {t('pages.gdpr.rights.title')}
          </h3>
          <ul className="list-disc list-inside space-y-2 text-yellow-800">
            {['access', 'rectification', 'erasure', 'restriction', 'portability', 'objection'].map((right) => (
              <li key={right}>{t(`pages.gdpr.rights.${right}`)}</li>
            ))}
          </ul>
        </div>
      </div>
    </PageLayout>
  );
}

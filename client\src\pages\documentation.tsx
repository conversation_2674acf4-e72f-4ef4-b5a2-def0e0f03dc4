import { useTranslation } from 'react-i18next';
import { PageLayout } from '@/components/page-layout';
import { Book, Code, Zap, Shield } from 'lucide-react';

export default function Documentation() {
  const { t } = useTranslation();

  const sections = [
    {
      key: 'quickStart',
      icon: Zap,
      color: 'bg-yellow-500'
    },
    {
      key: 'api',
      icon: Code,
      color: 'bg-blue-500'
    },
    {
      key: 'guides',
      icon: Book,
      color: 'bg-green-500'
    },
    {
      key: 'security',
      icon: Shield,
      color: 'bg-red-500'
    }
  ];

  return (
    <PageLayout 
      title={t('pages.documentation.title')} 
      subtitle={t('pages.documentation.subtitle')}
    >
      {/* Quick Navigation */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
        {sections.map((section) => {
          const Icon = section.icon;
          return (
            <div key={section.key} className="text-center p-6 border border-gray-200 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
              <div className={`w-16 h-16 mx-auto mb-4 ${section.color} rounded-full flex items-center justify-center`}>
                <Icon className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {t(`pages.documentation.sections.${section.key}.title`)}
              </h3>
              <p className="text-gray-600 text-sm">
                {t(`pages.documentation.sections.${section.key}.description`)}
              </p>
            </div>
          );
        })}
      </div>

      {/* Content Sections */}
      <div className="space-y-12">
        {/* Quick Start */}
        <section>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            {t('pages.documentation.quickStart.title')}
          </h2>
          <div className="bg-gray-50 rounded-lg p-6">
            <ol className="space-y-4">
              {['step1', 'step2', 'step3', 'step4'].map((step, index) => (
                <li key={step} className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-0.5">
                    {index + 1}
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">
                      {t(`pages.documentation.quickStart.${step}.title`)}
                    </h3>
                    <p className="text-gray-600 text-sm">
                      {t(`pages.documentation.quickStart.${step}.description`)}
                    </p>
                  </div>
                </li>
              ))}
            </ol>
          </div>
        </section>

        {/* API Reference */}
        <section>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            {t('pages.documentation.api.title')}
          </h2>
          <div className="space-y-6">
            {['upload', 'process', 'download'].map((endpoint) => (
              <div key={endpoint} className="border border-gray-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  {t(`pages.documentation.api.${endpoint}.title`)}
                </h3>
                <div className="bg-gray-900 text-white p-4 rounded-lg mb-4 overflow-x-auto">
                  <code className="text-sm">
                    {t(`pages.documentation.api.${endpoint}.code`)}
                  </code>
                </div>
                <p className="text-gray-600 text-sm">
                  {t(`pages.documentation.api.${endpoint}.description`)}
                </p>
              </div>
            ))}
          </div>
        </section>

        {/* Best Practices */}
        <section>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            {t('pages.documentation.bestPractices.title')}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {['imageQuality', 'fileSize', 'batchProcessing', 'errorHandling'].map((practice) => (
              <div key={practice} className="border border-gray-200 rounded-lg p-6">
                <h3 className="font-semibold text-gray-900 mb-2">
                  {t(`pages.documentation.bestPractices.${practice}.title`)}
                </h3>
                <p className="text-gray-600 text-sm">
                  {t(`pages.documentation.bestPractices.${practice}.description`)}
                </p>
              </div>
            ))}
          </div>
        </section>
      </div>
    </PageLayout>
  );
}

import { useTranslation } from 'react-i18next';
import { PageLayout } from '@/components/page-layout';
import { Check, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function Pricing() {
  const { t } = useTranslation();

  const plans = [
    {
      key: 'free',
      popular: false,
      price: '$0',
      period: 'month'
    },
    {
      key: 'pro',
      popular: true,
      price: '$9.99',
      period: 'month'
    },
    {
      key: 'business',
      popular: false,
      price: '$29.99',
      period: 'month'
    }
  ];

  const features = {
    free: ['basicRemoval', 'lowRes', 'watermark', 'support'],
    pro: ['advancedRemoval', 'highRes', 'noWatermark', 'prioritySupport', 'batchProcessing'],
    business: ['enterpriseRemoval', 'ultraHighRes', 'customBranding', 'dedicatedSupport', 'api', 'analytics']
  };

  return (
    <PageLayout 
      title={t('pages.pricing.title')} 
      subtitle={t('pages.pricing.subtitle')}
    >
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
        {plans.map((plan) => (
          <div key={plan.key} className={`relative rounded-lg border-2 p-8 ${plan.popular ? 'border-primary shadow-lg scale-105' : 'border-gray-200'}`}>
            {plan.popular && (
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <div className="bg-primary text-white px-4 py-1 rounded-full text-sm font-semibold flex items-center space-x-1">
                  <Star className="w-4 h-4" />
                  <span>{t('pages.pricing.popular')}</span>
                </div>
              </div>
            )}
            
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">
                {t(`pages.pricing.plans.${plan.key}.name`)}
              </h3>
              <div className="text-4xl font-bold text-primary mb-2">
                {plan.price}
              </div>
              <div className="text-gray-600">
                {t(`pages.pricing.plans.${plan.key}.period`)}
              </div>
            </div>

            <ul className="space-y-3 mb-8">
              {features[plan.key as keyof typeof features].map((feature) => (
                <li key={feature} className="flex items-center space-x-3">
                  <Check className="w-5 h-5 text-green-500 flex-shrink-0" />
                  <span className="text-gray-700">
                    {t(`pages.pricing.features.${feature}`)}
                  </span>
                </li>
              ))}
            </ul>

            <Button 
              className={`w-full ${plan.popular ? 'bg-primary hover:bg-primary/90' : 'bg-gray-900 hover:bg-gray-800'}`}
            >
              {t(`pages.pricing.plans.${plan.key}.button`)}
            </Button>
          </div>
        ))}
      </div>

      {/* FAQ */}
      <div className="bg-gray-50 rounded-lg p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
          {t('pages.pricing.faq.title')}
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {['question1', 'question2', 'question3', 'question4'].map((q) => (
            <div key={q}>
              <h3 className="font-semibold text-gray-900 mb-2">
                {t(`pages.pricing.faq.${q}.question`)}
              </h3>
              <p className="text-gray-600 text-sm">
                {t(`pages.pricing.faq.${q}.answer`)}
              </p>
            </div>
          ))}
        </div>
      </div>
    </PageLayout>
  );
}

import { useTranslation } from 'react-i18next';
import { PageLayout } from '@/components/page-layout';
import { Star, Quote } from 'lucide-react';

export default function Testimonials() {
  const { t } = useTranslation();

  const testimonials = [
    {
      key: 'review1',
      rating: 5,
      avatar: '👨‍💼'
    },
    {
      key: 'review2', 
      rating: 5,
      avatar: '👩‍🎨'
    },
    {
      key: 'review3',
      rating: 5,
      avatar: '👨‍💻'
    },
    {
      key: 'review4',
      rating: 5,
      avatar: '👩‍📸'
    },
    {
      key: 'review5',
      rating: 4,
      avatar: '👨‍🎨'
    },
    {
      key: 'review6',
      rating: 5,
      avatar: '👩‍💼'
    }
  ];

  const stats = [
    { key: 'users', value: '50,000+' },
    { key: 'images', value: '1M+' },
    { key: 'satisfaction', value: '98%' },
    { key: 'countries', value: '150+' }
  ];

  return (
    <PageLayout 
      title={t('pages.testimonials.title')} 
      subtitle={t('pages.testimonials.subtitle')}
    >
      {/* Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
        {stats.map((stat) => (
          <div key={stat.key} className="text-center">
            <div className="text-3xl font-bold text-primary mb-2">
              {stat.value}
            </div>
            <div className="text-gray-600">
              {t(`pages.testimonials.stats.${stat.key}`)}
            </div>
          </div>
        ))}
      </div>

      {/* Testimonials Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
        {testimonials.map((testimonial) => (
          <div key={testimonial.key} className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
            <div className="flex items-center mb-4">
              <div className="text-2xl mr-3">{testimonial.avatar}</div>
              <div>
                <div className="font-semibold text-gray-900">
                  {t(`pages.testimonials.reviews.${testimonial.key}.author`)}
                </div>
                <div className="text-sm text-gray-600">
                  {t(`pages.testimonials.reviews.${testimonial.key}.role`)}
                </div>
              </div>
            </div>
            
            <div className="flex mb-3">
              {[...Array(5)].map((_, i) => (
                <Star 
                  key={i} 
                  className={`w-4 h-4 ${i < testimonial.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} 
                />
              ))}
            </div>
            
            <div className="relative">
              <Quote className="w-6 h-6 text-gray-300 absolute -top-2 -left-1" />
              <p className="text-gray-700 pl-6">
                {t(`pages.testimonials.reviews.${testimonial.key}.content`)}
              </p>
            </div>
          </div>
        ))}
      </div>

      {/* Call to Action */}
      <div className="bg-gradient-to-r from-primary to-secondary rounded-lg p-8 text-center text-white">
        <h2 className="text-2xl font-bold mb-4">
          {t('pages.testimonials.cta.title')}
        </h2>
        <p className="text-lg mb-6 opacity-90">
          {t('pages.testimonials.cta.subtitle')}
        </p>
        <button className="bg-white text-primary px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
          {t('pages.testimonials.cta.button')}
        </button>
      </div>
    </PageLayout>
  );
}

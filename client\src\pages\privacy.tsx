import { useTranslation } from 'react-i18next';
import { PageLayout } from '@/components/page-layout';

export default function Privacy() {
  const { t } = useTranslation();

  const sections = [
    'introduction',
    'dataCollection',
    'dataUsage',
    'dataSecurity',
    'cookies',
    'thirdParty',
    'userRights',
    'dataRetention',
    'contact'
  ];

  return (
    <PageLayout 
      title={t('pages.privacy.title')} 
      subtitle={t('pages.privacy.subtitle')}
    >
      <div className="prose max-w-none">
        <div className="mb-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-blue-800 text-sm mb-0">
            <strong>{t('pages.privacy.lastUpdated')}:</strong> {t('pages.privacy.updateDate')}
          </p>
        </div>

        {sections.map((section) => (
          <section key={section} className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              {t(`pages.privacy.sections.${section}.title`)}
            </h2>
            <div className="text-gray-700 space-y-4">
              {t(`pages.privacy.sections.${section}.content`, { returnObjects: true }) as string[]}
              {(t(`pages.privacy.sections.${section}.content`, { returnObjects: true }) as string[]).map((paragraph: string, index: number) => (
                <p key={index}>{paragraph}</p>
              ))}
            </div>
          </section>
        ))}

        <div className="mt-12 p-6 bg-gray-50 border border-gray-200 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">
            {t('pages.privacy.contact.title')}
          </h3>
          <p className="text-gray-700 mb-4">
            {t('pages.privacy.contact.description')}
          </p>
          <div className="space-y-2 text-sm text-gray-600">
            <p><strong>{t('pages.privacy.contact.email')}:</strong> <EMAIL></p>
            <p><strong>{t('pages.privacy.contact.address')}:</strong> 123 Tech Street, San Francisco, CA 94105</p>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
